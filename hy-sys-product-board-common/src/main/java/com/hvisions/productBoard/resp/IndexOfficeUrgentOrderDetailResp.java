package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class IndexOfficeUrgentOrderDetailResp {
    //产品型号
    //订单总数量
    //计划下派百分比
    //原料准备百分比
    //生产执行百分比
    //外厂检验百分比
    //产品检验百分比
    //军方检验百分比
    //完工入库百分比
    @ApiModelProperty(value = "产品型号")
    private String productModel;
    @ApiModelProperty(value = "订单总数量")
    private Integer orderQuantity;
    @ApiModelProperty(value = "计划下派百分比")
    private Double planPercentage;
    @ApiModelProperty(value = "原料准备百分比")
    private Double rawPercentage;
    @ApiModelProperty(value = "生产执行百分比")
    private Double productionPercentage;
    @ApiModelProperty(value = "外厂检验百分比")
    private Double fInspectionPercentage;
    @ApiModelProperty(value = "产品检验百分比")
    private Double inspectionPercentage;
    @ApiModelProperty(value = "军方检验百分比")
    private Double mInspectionPercentage;
    @ApiModelProperty(value = "完工入库百分比")
    private Double deliveryPercentage;
}
