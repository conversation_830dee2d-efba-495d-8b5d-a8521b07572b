package com.hvisions.productBoard.service;

import com.hvisions.productBoard.entity.SaSaleOrderLine;
import com.hvisions.productBoard.entity.HyWmsDeliverOrder;
import com.hvisions.productBoard.entity.HyQmVulcanizeQualityOrder;
import com.hvisions.productBoard.entity.HyQmProductCheckSubmit;
import com.hvisions.productBoard.mapper.SaSaleOrderLineMapper;
import com.hvisions.productBoard.mapper.HyWmsDeliverOrderMapper;
import com.hvisions.productBoard.mapper.HyQmVulcanizeQualityOrderMapper;
import com.hvisions.productBoard.mapper.HyQmProductCheckSubmitMapper;
import com.hvisions.productBoard.resp.IndexOfficeUrgentOrderDetailResp;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IndexOfficeServiceTest {

    @Mock
    private SaSaleOrderLineMapper saSaleOrderLineMapper;

    @Mock
    private HyWmsDeliverOrderMapper hyWmsDeliverOrderMapper;

    @Mock
    private HyQmVulcanizeQualityOrderMapper hyQmVulcanizeQualityOrderMapper;

    @Mock
    private HyQmProductCheckSubmitMapper hyQmProductCheckSubmitMapper;

    @InjectMocks
    private IndexOfficeService indexOfficeService;

    @Test
    void testGetUrgentOrderDetail() {
        // 准备测试数据
        String saleOrderCode = "TEST001";
        
        SaSaleOrderLine saleOrderLine = new SaSaleOrderLine();
        saleOrderLine.setSaleOrderCode(saleOrderCode);
        saleOrderLine.setProductModel("TEST-MODEL");
        saleOrderLine.setPlanCount(new BigDecimal("100"));
        saleOrderLine.setDispatchCount(new BigDecimal("80"));
        saleOrderLine.setProductionCount(new BigDecimal("60"));
        saleOrderLine.setQualityCount(new BigDecimal("50"));
        saleOrderLine.setMilitaryQualityCount(new BigDecimal("40"));
        saleOrderLine.setWareCount(new BigDecimal("30"));

        HyWmsDeliverOrder deliverOrder = new HyWmsDeliverOrder();
        deliverOrder.setSaleOrderCode(saleOrderCode);
        deliverOrder.setNeedQuantity(new BigDecimal("100"));
        deliverOrder.setSubmitQuantity(new BigDecimal("70"));

        HyQmVulcanizeQualityOrder vulcanizeOrder = new HyQmVulcanizeQualityOrder();
        vulcanizeOrder.setSaleOrderCode(saleOrderCode);
        vulcanizeOrder.setTotalCount(100);
        vulcanizeOrder.setQualityPassCount(85);

        // 军检提交单（验收提交单号不以ZJZ结尾）
        HyQmProductCheckSubmit militarySubmit = new HyQmProductCheckSubmit();
        militarySubmit.setSaleOrderCode(saleOrderCode);
        militarySubmit.setSubmitOrderCode("SUBMIT001"); // 不以ZJZ结尾，是军检
        militarySubmit.setSubmitQuantity(50);
        militarySubmit.setQualifiedQuantity(45);

        // Mock 方法调用
        when(saSaleOrderLineMapper.selectList(any())).thenReturn(Arrays.asList(saleOrderLine));
        when(hyWmsDeliverOrderMapper.selectList(any())).thenReturn(Arrays.asList(deliverOrder));
        when(hyQmVulcanizeQualityOrderMapper.selectList(any())).thenReturn(Arrays.asList(vulcanizeOrder));
        when(hyQmProductCheckSubmitMapper.selectList(any())).thenReturn(Arrays.asList(militarySubmit));

        // 执行测试
        List<IndexOfficeUrgentOrderDetailResp> result = indexOfficeService.getUrgentOrderDetail(saleOrderCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        IndexOfficeUrgentOrderDetailResp resp = result.get(0);
        assertEquals("TEST-MODEL", resp.getProductModel());
        assertEquals(100, resp.getOrderQuantity());
        assertEquals(80.0, resp.getPlanPercentage()); // 80/100 * 100
        assertEquals(70.0, resp.getRawPercentage()); // 70/100 * 100
        assertEquals(60.0, resp.getProductionPercentage()); // 60/100 * 100
        assertEquals(85.0, resp.getFInspectionPercentage()); // 85/100 * 100
        assertEquals(50.0, resp.getInspectionPercentage()); // 50/100 * 100
        assertEquals(90.0, resp.getMInspectionPercentage()); // 45/50 * 100 (从产品验收提交单计算)
        assertEquals(30.0, resp.getDeliveryPercentage()); // 30/100 * 100
    }

    @Test
    void testGetUrgentOrderDetailWithEmptyResult() {
        // 准备测试数据
        String saleOrderCode = "EMPTY001";

        // Mock 方法调用返回空列表
        when(saSaleOrderLineMapper.selectList(any())).thenReturn(Arrays.asList());

        // 执行测试
        List<IndexOfficeUrgentOrderDetailResp> result = indexOfficeService.getUrgentOrderDetail(saleOrderCode);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testMilitaryInspectionWithZJZSuffix() {
        // 测试验收提交单号以ZJZ结尾的情况（应该被过滤掉，不计入军检）
        String saleOrderCode = "TEST002";
        
        SaSaleOrderLine saleOrderLine = new SaSaleOrderLine();
        saleOrderLine.setSaleOrderCode(saleOrderCode);
        saleOrderLine.setProductModel("TEST-MODEL-2");
        saleOrderLine.setPlanCount(new BigDecimal("100"));
        saleOrderLine.setDispatchCount(new BigDecimal("80"));
        saleOrderLine.setProductionCount(new BigDecimal("60"));
        saleOrderLine.setQualityCount(new BigDecimal("50"));
        saleOrderLine.setWareCount(new BigDecimal("30"));

        // 质检提交单（验收提交单号以ZJZ结尾，应该被过滤掉）
        HyQmProductCheckSubmit qualitySubmit = new HyQmProductCheckSubmit();
        qualitySubmit.setSaleOrderCode(saleOrderCode);
        qualitySubmit.setSubmitOrderCode("SUBMIT001ZJZ"); // 以ZJZ结尾，是质检，不是军检
        qualitySubmit.setSubmitQuantity(50);
        qualitySubmit.setQualifiedQuantity(45);

        // Mock 方法调用
        when(saSaleOrderLineMapper.selectList(any())).thenReturn(Arrays.asList(saleOrderLine));
        when(hyWmsDeliverOrderMapper.selectList(any())).thenReturn(Arrays.asList());
        when(hyQmVulcanizeQualityOrderMapper.selectList(any())).thenReturn(Arrays.asList());
        when(hyQmProductCheckSubmitMapper.selectList(any())).thenReturn(Arrays.asList()); // 返回空，因为ZJZ结尾的被过滤

        // 执行测试
        List<IndexOfficeUrgentOrderDetailResp> result = indexOfficeService.getUrgentOrderDetail(saleOrderCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        
        IndexOfficeUrgentOrderDetailResp resp = result.get(0);
        assertEquals(0.0, resp.getMInspectionPercentage()); // 军检百分比应该为0，因为没有军检记录
    }
}
