<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.productBoard.mapper.HyWmsDeliverOrderMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.productBoard.entity.HyWmsDeliverOrder">
    <!--@mbg.generated-->
    <!--@Table sys_wms.hy_wms_deliver_order-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="site_num" jdbcType="VARCHAR" property="siteNum" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="back_type" jdbcType="INTEGER" property="backType" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="from_location" jdbcType="VARCHAR" property="fromLocation" />
    <result column="from_location_code" jdbcType="VARCHAR" property="fromLocationCode" />
    <result column="from_quantity" jdbcType="DECIMAL" property="fromQuantity" />
    <result column="is_close" jdbcType="BIT" property="isClose" />
    <result column="material_group" jdbcType="VARCHAR" property="materialGroup" />
    <result column="material_group_name" jdbcType="VARCHAR" property="materialGroupName" />
    <result column="need_quantity" jdbcType="DECIMAL" property="needQuantity" />
    <result column="operation" jdbcType="INTEGER" property="operation" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="plan_quantity" jdbcType="DECIMAL" property="planQuantity" />
    <result column="product_eigenvalue" jdbcType="VARCHAR" property="productEigenvalue" />
    <result column="product_model" jdbcType="VARCHAR" property="productModel" />
    <result column="sale_order_code" jdbcType="VARCHAR" property="saleOrderCode" />
    <result column="state" jdbcType="INTEGER" property="state" />
    <result column="submit_quantity" jdbcType="DECIMAL" property="submitQuantity" />
    <result column="submit_times" jdbcType="INTEGER" property="submitTimes" />
    <result column="supplement_quantity" jdbcType="DECIMAL" property="supplementQuantity" />
    <result column="target_location" jdbcType="VARCHAR" property="targetLocation" />
    <result column="target_location_code" jdbcType="VARCHAR" property="targetLocationCode" />
    <result column="target_quantity" jdbcType="DECIMAL" property="targetQuantity" />
    <result column="print_state" jdbcType="INTEGER" property="printState" />
    <result column="print_times" jdbcType="INTEGER" property="printTimes" />
    <result column="submit_state" jdbcType="INTEGER" property="submitState" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="biz_order_type" jdbcType="INTEGER" property="bizOrderType" />
    <result column="biz_order_type_name" jdbcType="VARCHAR" property="bizOrderTypeName" />
    <result column="rsnum" jdbcType="VARCHAR" property="rsnum" />
    <result column="consume_quantity" jdbcType="DECIMAL" property="consumeQuantity" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, create_time, creator_id, site_num, update_time, updater_id, back_type, category, 
    code, creator_name, department, from_location, from_location_code, from_quantity, 
    is_close, material_group, material_group_name, need_quantity, `operation`, order_code, 
    order_type, plan_quantity, product_eigenvalue, product_model, sale_order_code, `state`, 
    submit_quantity, submit_times, supplement_quantity, target_location, target_location_code, 
    target_quantity, print_state, print_times, submit_state, remark, biz_order_type, 
    biz_order_type_name, rsnum, consume_quantity
  </sql>
</mapper>