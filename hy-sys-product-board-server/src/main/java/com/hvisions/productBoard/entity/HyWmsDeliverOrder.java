package com.hvisions.productBoard.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移库单
 */
@Data
@EqualsAndHashCode(callSuper=true)
@TableName("sys_wms.hy_wms_deliver_order")
public class HyWmsDeliverOrder extends SysBase {
    /**
    * 退料类型
    */
    private Integer backType;

    /**
    * 类别
    */
    private String category;

    /**
    * 移库单号
    */
    private String code;

    /**
    * 创建人
    */
    private String creatorName;

    /**
    * 领料部门
    */
    private String department;

    /**
    * 来源库位
    */
    private String fromLocation;

    /**
    * 来源库位
    */
    private String fromLocationCode;

    /**
    * 发出数量
    */
    private BigDecimal fromQuantity;

    private Boolean isClose;

    /**
    * 物料组
    */
    private String materialGroup;

    /**
    * 物料组
    */
    private String materialGroupName;

    /**
    * 需求量
    */
    private BigDecimal needQuantity;

    /**
    * 业务类型
    */
    private Integer operation;

    /**
    * 业务单号
    */
    private String orderCode;

    /**
    * 单据类别
    */
    private Integer orderType;

    /**
    * 计划领料数量
    */
    private BigDecimal planQuantity;

    /**
    * 产品规格
    */
    private String productEigenvalue;

    /**
    * 产品型号
    */
    private String productModel;

    /**
    * 销售订单号
    */
    private String saleOrderCode;

    /**
    * 发出方状态
    */
    private Integer state;

    /**
    * 已提交数量
    */
    private BigDecimal submitQuantity;

    /**
    * 提交次数
    */
    private Integer submitTimes;

    /**
    * 补领数量
    */
    private BigDecimal supplementQuantity;

    /**
    * 目标库位
    */
    private String targetLocation;

    /**
    * 目标库位
    */
    private String targetLocationCode;

    /**
    * 接受数量
    */
    private BigDecimal targetQuantity;

    /**
    * 打印状态
    */
    private Integer printState;

    /**
    * 打印次数
    */
    private Integer printTimes;

    /**
    * 提交状态
    */
    private Integer submitState;

    private String remark;

    /**
    * 业务单号类型
    */
    private Integer bizOrderType;

    /**
    * 业务单号类型
    */
    private String bizOrderTypeName;

    /**
    * 预留单号
    */
    private String rsnum;

    /**
    * 科研消耗数量
    */
    private BigDecimal consumeQuantity;
}