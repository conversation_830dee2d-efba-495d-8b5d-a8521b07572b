package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.productBoard.entity.*;
import com.hvisions.productBoard.enums.DepartmentEnum;
import com.hvisions.productBoard.enums.InspectionTypeEnum;
import com.hvisions.productBoard.enums.PackageIndexTypeEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.mapper.*;
import com.hvisions.productBoard.mapper.HyQmProductCheckSubmitMapper;
import com.hvisions.productBoard.req.CommonPageReq;
import com.hvisions.productBoard.resp.CompleteResp;
import com.hvisions.productBoard.resp.IndexOfficeDataOverviewResp;
import com.hvisions.productBoard.resp.IndexOfficeUrgentOrderDetailResp;
import com.hvisions.productBoard.resp.IndexOfficeUrgentOrderResp;
import com.hvisions.productBoard.util.PageHelperAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexOfficeService {

    @Resource
    private PmEnhanceService pmEnhanceService;

    @Resource
    private PmMoldingService pmMoldingService;

    @Resource
    private PmInspectionService pmInspectionService;

    @Resource
    private PmPackageService pmPackageService;

    @Resource
    private SaSaleOrderMapper saSaleOrderMapper;

    @Resource
    private HyWmsCompleteDeliverOrderMapper hyWmsCompleteDeliverOrderMapper;

    @Resource
    private SaSaleOrderLineMapper saSaleOrderLineMapper;

    @Resource
    private HyWmsDeliverOrderMapper hyWmsDeliverOrderMapper;

    @Resource
    private HyQmVulcanizeQualityOrderMapper hyQmVulcanizeQualityOrderMapper;

    @Resource
    private HyQmProductCheckSubmitMapper hyQmProductCheckSubmitMapper;

    public IndexOfficeDataOverviewResp getDataOverview() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

        Long newOrderCountLong = saSaleOrderMapper.selectCount(
                new QueryWrapper<SaSaleOrder>().lambda()
                        .between(SaSaleOrder::getCreateTime, todayStart, todayEnd)
        );
        Integer newOrderCount = newOrderCountLong != null ? newOrderCountLong.intValue() : 0;

        List<HyWmsCompleteDeliverOrder> deliverOrders = hyWmsCompleteDeliverOrderMapper.selectList(
                new QueryWrapper<HyWmsCompleteDeliverOrder>().lambda()
                        .between(HyWmsCompleteDeliverOrder::getFinishDate, todayStart, todayEnd)
                        .isNotNull(HyWmsCompleteDeliverOrder::getActualQuantity)
        );

        BigDecimal totalQuantity = deliverOrders.stream()
                .map(order -> order.getActualQuantity() != null ? order.getActualQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer productQuantity = totalQuantity.intValue();

        // TODO 瓶颈订单数
        Integer bottleneckOrderCount = 0;

        Long errorOrderCountLong = saSaleOrderMapper.selectCount(
                new QueryWrapper<SaSaleOrder>().lambda()
                        .lt(SaSaleOrder::getPlanFinishDate, today)
                        .notIn(SaSaleOrder::getShippingState, 20, 40)
        );
        Integer errorOrderCount = errorOrderCountLong != null ? errorOrderCountLong.intValue() : 0;

        LocalDate oneMonthLater = today.plusMonths(1);
        Long nearOrderCountLong = saSaleOrderMapper.selectCount(
                new QueryWrapper<SaSaleOrder>().lambda()
                        .ge(SaSaleOrder::getPlanFinishDate, today)
                        .le(SaSaleOrder::getPlanFinishDate, oneMonthLater)
                        .notIn(SaSaleOrder::getShippingState, 20, 40)
        );
        Integer nearOrderCount = nearOrderCountLong != null ? nearOrderCountLong.intValue() : 0;

        return IndexOfficeDataOverviewResp.builder()
                .newOrderCount(newOrderCount)
                .productQuantity(productQuantity)
                .bottleneckOrderCount(bottleneckOrderCount)
                .errorOrderCount(errorOrderCount)
                .nearOrderCount(nearOrderCount)
                .build();
    }

    public List<CompleteResp> getCompleteRateYear(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.YEAR);
    }

    public List<CompleteResp> getCompleteRateMonth(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.MONTH);
    }

    public List<CompleteResp> getCompleteRateWeek(DepartmentEnum department) {
        return getCompleteRateByDepartmentAndPeriod(department, TimePeriodEnum.WEEK);
    }

    @SuppressWarnings("unchecked")
    private List<CompleteResp> getCompleteRateByDepartmentAndPeriod(DepartmentEnum department, TimePeriodEnum period) {
        switch (department) {
            case ENHANCE:
                return (List<CompleteResp>) (List<?>) pmEnhanceService.getCompleteRate(period);
            case MOLDING:
                return (List<CompleteResp>) (List<?>) pmMoldingService.getCompleteRate(period);
            case INSPECTION:
                return (List<CompleteResp>) (List<?>) pmInspectionService.getOutput(period, InspectionTypeEnum.QUALITY);
            case PACKAGE:
                return (List<CompleteResp>) (List<?>) pmPackageService.getOutput(period, PackageIndexTypeEnum.PACKAGE);
            default:
                throw new IllegalArgumentException("不支持的部门类型: " + department);
        }
    }

    public Page<IndexOfficeUrgentOrderResp> getUrgentOrder(Integer current, Integer size, String type, String contractCode) {
        if ("销售".equals(type)) {
            return getSalesUrgentOrder(current, size, contractCode);
        } else if ("科研".equals(type) || "试验".equals(type)) {
            return null;
        } else {
            throw new IllegalArgumentException("不支持的订单类型: " + type);
        }
    }

    private Page<IndexOfficeUrgentOrderResp> getSalesUrgentOrder(Integer current, Integer size, String contractCode) {
        CommonPageReq pageReq = PageHelperAdapter.createPageReq(current, size);

        return PageHelperUtil.getPage(
                req -> getSalesOrderList(contractCode).stream()
                        .map(this::convertToUrgentOrderResp)
                        .collect(Collectors.toList()),
                pageReq);
    }

    private List<SaSaleOrder> getSalesOrderList(String contractCode) {
        LocalDate today = LocalDate.now();
        LocalDate oneMonthLater = today.plusMonths(1);

        LambdaQueryWrapper<SaSaleOrder> queryWrapper = new QueryWrapper<SaSaleOrder>().lambda()
                .notIn(SaSaleOrder::getShippingState, 20, 40);

        if (StringUtils.hasText(contractCode)) {
            queryWrapper.like(SaSaleOrder::getContractCode, contractCode);
        } else {
            queryWrapper.ge(SaSaleOrder::getPlanFinishDate, today)
                    .le(SaSaleOrder::getPlanFinishDate, oneMonthLater);
        }

        queryWrapper.orderByAsc(SaSaleOrder::getPlanFinishDate);

        return saSaleOrderMapper.selectList(queryWrapper);
    }

    private IndexOfficeUrgentOrderResp convertToUrgentOrderResp(SaSaleOrder saleOrder) {

        BigDecimal progress = BigDecimal.ZERO;
        if (saleOrder.getPlanCount() != null && saleOrder.getPlanCount().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal completedCount = saleOrder.getWareCount() != null ? saleOrder.getWareCount() : BigDecimal.ZERO;
            progress = completedCount.divide(saleOrder.getPlanCount(), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }

        String progressStr = progress.setScale(2, RoundingMode.HALF_UP) + "%";

        return IndexOfficeUrgentOrderResp.builder()
                .contractCode(saleOrder.getContractCode())
                .customerName(saleOrder.getCustomerName())
                .deliveryDate(saleOrder.getPlanFinishDate() != null ?
                        saleOrder.getPlanFinishDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : "")
                .progress(progressStr)
                .build();
    }


    public List<IndexOfficeUrgentOrderDetailResp> getUrgentOrderDetail(String saleOrderCode) {

        List<SaSaleOrderLine> saleOrderLines = saSaleOrderLineMapper.selectList(
                new QueryWrapper<SaSaleOrderLine>().lambda()
                        .eq(SaSaleOrderLine::getSaleOrderCode, saleOrderCode)
        );

        if (saleOrderLines.isEmpty()) {
            return new ArrayList<>();
        }

        return saleOrderLines.stream()
                .map(this::convertToUrgentOrderDetailResp)
                .collect(Collectors.toList());
    }


    private IndexOfficeUrgentOrderDetailResp convertToUrgentOrderDetailResp(SaSaleOrderLine saleOrderLine) {
        String saleOrderCode = saleOrderLine.getSaleOrderCode();

        Double planPercentage = calculatePlanPercentage(saleOrderLine);
        Double rawPercentage = calculateRawPercentage(saleOrderCode);
        Double productionPercentage = calculateProductionPercentage(saleOrderLine);
        Double fInspectionPercentage = calculateFInspectionPercentage(saleOrderCode);
        Double inspectionPercentage = calculateInspectionPercentage(saleOrderLine);
        Double mInspectionPercentage = calculateMInspectionPercentage(saleOrderLine);
        Double deliveryPercentage = calculateDeliveryPercentage(saleOrderLine);

        return IndexOfficeUrgentOrderDetailResp.builder()
                .productModel(saleOrderLine.getProductModel())
                .orderQuantity(saleOrderLine.getPlanCount() != null ? saleOrderLine.getPlanCount().intValue() : 0)
                .planPercentage(planPercentage)
                .rawPercentage(rawPercentage)
                .productionPercentage(productionPercentage)
                .fInspectionPercentage(fInspectionPercentage)
                .inspectionPercentage(inspectionPercentage)
                .mInspectionPercentage(mInspectionPercentage)
                .deliveryPercentage(deliveryPercentage)
                .build();
    }


    private Double calculatePlanPercentage(SaSaleOrderLine saleOrderLine) {
        if (saleOrderLine.getPlanCount() == null || saleOrderLine.getPlanCount().compareTo(BigDecimal.ZERO) == 0) {
            return 0.0;
        }

        BigDecimal dispatchCount = saleOrderLine.getDispatchCount() != null ? saleOrderLine.getDispatchCount() : BigDecimal.ZERO;
        return dispatchCount.divide(saleOrderLine.getPlanCount(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
    }


    private Double calculateRawPercentage(String saleOrderCode) {
        List<HyWmsDeliverOrder> deliverOrders = hyWmsDeliverOrderMapper.selectList(
                new QueryWrapper<HyWmsDeliverOrder>().lambda()
                        .eq(HyWmsDeliverOrder::getSaleOrderCode, saleOrderCode)
        );

        if (deliverOrders.isEmpty()) {
            return 0.0;
        }

        BigDecimal totalPlanQuantity = deliverOrders.stream()
                .map(order -> order.getPlanQuantity() != null ? order.getPlanQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalFromQuantity = deliverOrders.stream()
                .map(order -> order.getFromQuantity() != null ? order.getFromQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalPlanQuantity.compareTo(BigDecimal.ZERO) == 0) {
            return 0.0;
        }

        return totalFromQuantity.divide(totalPlanQuantity, 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
    }


    private Double calculateProductionPercentage(SaSaleOrderLine saleOrderLine) {
        if (saleOrderLine.getPlanCount() == null || saleOrderLine.getPlanCount().compareTo(BigDecimal.ZERO) == 0) {
            return 0.0;
        }

        BigDecimal productionCount = saleOrderLine.getProductionCount() != null ? saleOrderLine.getProductionCount() : BigDecimal.ZERO;
        return productionCount.divide(saleOrderLine.getPlanCount(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
    }


    private Double calculateFInspectionPercentage(String saleOrderCode) {
        List<HyQmVulcanizeQualityOrder> vulcanizeOrders = hyQmVulcanizeQualityOrderMapper.selectList(
                new QueryWrapper<HyQmVulcanizeQualityOrder>().lambda()
                        .eq(HyQmVulcanizeQualityOrder::getSaleOrderCode, saleOrderCode)
        );

        if (vulcanizeOrders.isEmpty()) {
            return 0.0;
        }

        int totalCount = vulcanizeOrders.stream()
                .mapToInt(order -> order.getTotalCount() != null ? order.getTotalCount() : 0)
                .sum();

        int qualityPassCount = vulcanizeOrders.stream()
                .mapToInt(order -> order.getQualityPassCount() != null ? order.getQualityPassCount() : 0)
                .sum();

        if (totalCount == 0) {
            return 0.0;
        }

        return (double) qualityPassCount / totalCount * 100;
    }


    private Double calculateInspectionPercentage(SaSaleOrderLine saleOrderLine) {
        if (saleOrderLine.getPlanCount() == null || saleOrderLine.getPlanCount().compareTo(BigDecimal.ZERO) == 0) {
            return 0.0;
        }

        BigDecimal qualityCount = saleOrderLine.getQualityCount() != null ? saleOrderLine.getQualityCount() : BigDecimal.ZERO;
        return qualityCount.divide(saleOrderLine.getPlanCount(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
    }


    /**
     * 计算军方检验百分比 - 从产品验收提交单获取，筛选验收提交单号不以ZJZ结尾的记录
     */
    private Double calculateMInspectionPercentage(SaSaleOrderLine saleOrderLine) {
        String saleOrderCode = saleOrderLine.getSaleOrderCode();

        // 查询产品验收提交单，筛选验收提交单号不以ZJZ结尾的记录（军检）
        List<HyQmProductCheckSubmit> militarySubmits = hyQmProductCheckSubmitMapper.selectList(
                new QueryWrapper<HyQmProductCheckSubmit>().lambda()
                        .eq(HyQmProductCheckSubmit::getSaleOrderCode, saleOrderCode)
                        .notLike(HyQmProductCheckSubmit::getSubmitOrderCode, "%ZJZ")
        );

        if (militarySubmits.isEmpty()) {
            return 0.0;
        }

        // 计算总提交数量和合格数量
        Integer totalSubmitQuantity = militarySubmits.stream()
                .mapToInt(submit -> submit.getSubmitQuantity() != null ? submit.getSubmitQuantity() : 0)
                .sum();

        Integer totalQualifiedQuantity = militarySubmits.stream()
                .mapToInt(submit -> submit.getQualifiedQuantity() != null ? submit.getQualifiedQuantity() : 0)
                .sum();

        if (totalSubmitQuantity == 0) {
            return 0.0;
        }

        return (double) totalQualifiedQuantity / totalSubmitQuantity * 100;
    }


    private Double calculateDeliveryPercentage(SaSaleOrderLine saleOrderLine) {
        if (saleOrderLine.getPlanCount() == null || saleOrderLine.getPlanCount().compareTo(BigDecimal.ZERO) == 0) {
            return 0.0;
        }

        BigDecimal wareCount = saleOrderLine.getWareCount() != null ? saleOrderLine.getWareCount() : BigDecimal.ZERO;
        return wareCount.divide(saleOrderLine.getPlanCount(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100)).doubleValue();
    }
}
